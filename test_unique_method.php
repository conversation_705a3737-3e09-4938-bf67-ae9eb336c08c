<?php
require_once('system/startup_sequence.php');

use system\database;
use system\Schema;

echo "<h1>Testing Blueprint unique() method</h1>\n";

try {
    // Test Blueprint SQL generation with unique constraint
    $blueprint = new system\Blueprint('test_unique_table');
    $blueprint->increments('id');
    $blueprint->string('table_name', 255)->unique();
    $blueprint->string('route_key', 255);
    $blueprint->timestamps();
    
    // Get the columns array via reflection to see the generated SQL
    $reflection = new ReflectionClass($blueprint);
    $columnsProperty = $reflection->getProperty('columns');
    $columnsProperty->setAccessible(true);
    $columns = $columnsProperty->getValue($blueprint);
    
    echo "<h2>Generated Column Definitions:</h2>\n";
    foreach ($columns as $i => $column) {
        echo ($i + 1) . ". " . htmlspecialchars($column) . "<br>\n";
    }
    
    // Test the actual table creation (but don't execute it)
    echo "<h2>Generated CREATE TABLE SQL:</h2>\n";
    $sql = "CREATE TABLE test_unique_table (" . implode(", ", $columns) . ")";
    echo "<pre>" . htmlspecialchars($sql) . "</pre>\n";
    
    echo "<p>✅ Blueprint unique() method works correctly!</p>\n";
    
} catch (Exception $e) {
    echo "<p>❌ Error testing unique() method: " . htmlspecialchars($e->getMessage()) . "</p>\n";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>\n";
}

echo "<h2>Testing table_config_manager table creation</h2>\n";

try {
    // Test the actual table creation that was failing
    $result = system\table_config_manager::ensure_config_table_exists();
    
    if ($result) {
        echo "<p>✅ table_config_manager table creation successful!</p>\n";
    } else {
        echo "<p>❌ table_config_manager table creation failed</p>\n";
    }
    
} catch (Exception $e) {
    echo "<p>❌ Error in table_config_manager: " . htmlspecialchars($e->getMessage()) . "</p>\n";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>\n";
}
