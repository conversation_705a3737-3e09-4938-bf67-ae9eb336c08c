<?php
// Simple test without database connection
require_once('system/classes/database.class.php');

echo "<h1>Testing Blueprint unique() method (No DB Connection Required)</h1>\n";

try {
    // Test Blueprint SQL generation with unique constraint
    $blueprint = new system\Blueprint('test_unique_table');
    $blueprint->increments('id');
    $blueprint->string('table_name', 255)->unique();
    $blueprint->string('route_key', 255);
    
    // Get the columns array via reflection to see the generated SQL
    $reflection = new ReflectionClass($blueprint);
    $columnsProperty = $reflection->getProperty('columns');
    $columnsProperty->setAccessible(true);
    $columns = $columnsProperty->getValue($blueprint);
    
    echo "<h2>Generated Column Definitions:</h2>\n";
    foreach ($columns as $i => $column) {
        echo ($i + 1) . ". " . htmlspecialchars($column) . "<br>\n";
    }
    
    // Test the actual table creation SQL generation
    echo "<h2>Generated CREATE TABLE SQL:</h2>\n";
    $sql = "CREATE TABLE test_unique_table (" . implode(", ", $columns) . ")";
    echo "<pre>" . htmlspecialchars($sql) . "</pre>\n";
    
    // Check if the unique constraint is properly added
    $tableNameColumn = $columns[1]; // Should be the table_name column
    if (strpos($tableNameColumn, 'UNIQUE') !== false) {
        echo "<p>✅ Blueprint unique() method works correctly! UNIQUE constraint found in column definition.</p>\n";
    } else {
        echo "<p>❌ Blueprint unique() method failed! UNIQUE constraint not found.</p>\n";
    }
    
} catch (Exception $e) {
    echo "<p>❌ Error testing unique() method: " . htmlspecialchars($e->getMessage()) . "</p>\n";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>\n";
}
